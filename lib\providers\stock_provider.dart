import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/stock_quote.dart';
import '../models/symbol_search_result.dart';
import '../models/historical_data.dart';
import '../models/api_error.dart';
import '../services/finnhub_api_service.dart';
import '../services/performance_service.dart';

enum LoadingState {
  idle,
  loading,
  success,
  error,
}

class StockProvider with ChangeNotifier {
  final FinnhubApiService _apiService;
  final PerformanceService _performanceService;
  
  // State variables
  LoadingState _loadingState = LoadingState.idle;
  LoadingState _searchLoadingState = LoadingState.idle;
  
  StockQuote? _currentQuote;
  PerformanceData? _performanceData;
  List<SymbolSearchResult> _searchResults = [];
  String? _currentSymbol;
  String? _errorMessage;
  String? _searchErrorMessage;
  
  // Recent searches cache
  List<String> _recentSearches = [];
  static const int _maxRecentSearches = 10;

  StockProvider(this._apiService) : _performanceService = PerformanceService(_apiService) {
    _loadRecentSearches();
  }

  // Getters
  LoadingState get loadingState => _loadingState;
  LoadingState get searchLoadingState => _searchLoadingState;
  StockQuote? get currentQuote => _currentQuote;
  PerformanceData? get performanceData => _performanceData;
  List<SymbolSearchResult> get searchResults => _searchResults;
  String? get currentSymbol => _currentSymbol;
  String? get errorMessage => _errorMessage;
  String? get searchErrorMessage => _searchErrorMessage;
  List<String> get recentSearches => _recentSearches;

  bool get isLoading => _loadingState == LoadingState.loading;
  bool get isSearchLoading => _searchLoadingState == LoadingState.loading;
  bool get hasError => _loadingState == LoadingState.error;
  bool get hasSearchError => _searchLoadingState == LoadingState.error;
  bool get hasData => _currentQuote != null;

  Future<void> fetchStockData(String symbol) async {
    _setLoadingState(LoadingState.loading);
    _errorMessage = null;
    _currentSymbol = symbol.toUpperCase();
    
    try {
      // Fetch stock quote
      final quote = await _apiService.getStockQuote(symbol);
      _currentQuote = quote;
      
      // Add to recent searches
      await _addToRecentSearches(symbol.toUpperCase());
      
      // Fetch performance data
      _fetchPerformanceData(symbol, quote.currentPrice);
      
      _setLoadingState(LoadingState.success);
    } catch (e) {
      _currentQuote = null;
      _performanceData = null;
      _errorMessage = _getErrorMessage(e);
      _setLoadingState(LoadingState.error);
    }
  }

  Future<void> _fetchPerformanceData(String symbol, double currentPrice) async {
    try {
      final performance = await _performanceService.getPerformanceData(symbol, currentPrice);
      _performanceData = performance;
      notifyListeners();
    } catch (e) {
      // Performance data is optional, don't fail the main request
      _performanceData = null;
      notifyListeners();
    }
  }

  Future<void> searchSymbols(String query) async {
    if (query.trim().isEmpty) {
      _searchResults = [];
      _searchErrorMessage = null;
      _setSearchLoadingState(LoadingState.idle);
      return;
    }

    _setSearchLoadingState(LoadingState.loading);
    _searchErrorMessage = null;
    
    try {
      final response = await _apiService.searchSymbols(query);
      _searchResults = response.result
          .where((result) => result.type == 'Common Stock')
          .take(10)
          .toList();
      _setSearchLoadingState(LoadingState.success);
    } catch (e) {
      _searchResults = [];
      _searchErrorMessage = _getErrorMessage(e);
      _setSearchLoadingState(LoadingState.error);
    }
  }

  void clearSearch() {
    _searchResults = [];
    _searchErrorMessage = null;
    _setSearchLoadingState(LoadingState.idle);
  }

  void clearError() {
    _errorMessage = null;
    if (_loadingState == LoadingState.error) {
      _setLoadingState(LoadingState.idle);
    }
  }

  void clearSearchError() {
    _searchErrorMessage = null;
    if (_searchLoadingState == LoadingState.error) {
      _setSearchLoadingState(LoadingState.idle);
    }
  }

  Future<void> _addToRecentSearches(String symbol) async {
    if (_recentSearches.contains(symbol)) {
      _recentSearches.remove(symbol);
    }
    
    _recentSearches.insert(0, symbol);
    
    if (_recentSearches.length > _maxRecentSearches) {
      _recentSearches = _recentSearches.take(_maxRecentSearches).toList();
    }
    
    await _saveRecentSearches();
    notifyListeners();
  }

  Future<void> _loadRecentSearches() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final searches = prefs.getStringList('recent_searches') ?? [];
      _recentSearches = searches;
      notifyListeners();
    } catch (e) {
      // Ignore errors when loading recent searches
    }
  }

  Future<void> _saveRecentSearches() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList('recent_searches', _recentSearches);
    } catch (e) {
      // Ignore errors when saving recent searches
    }
  }

  void _setLoadingState(LoadingState state) {
    _loadingState = state;
    notifyListeners();
  }

  void _setSearchLoadingState(LoadingState state) {
    _searchLoadingState = state;
    notifyListeners();
  }

  String _getErrorMessage(dynamic error) {
    if (error is ApiError) {
      return error.message;
    }
    return 'An unexpected error occurred';
  }

  @override
  void dispose() {
    _apiService.dispose();
    super.dispose();
  }
}
