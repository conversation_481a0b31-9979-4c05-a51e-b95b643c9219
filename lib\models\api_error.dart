class ApiError implements Exception {
  final String message;
  final int? statusCode;
  final String? details;

  const ApiError({
    required this.message,
    this.statusCode,
    this.details,
  });

  @override
  String toString() {
    return 'ApiError: $message${statusCode != null ? ' (Status: $statusCode)' : ''}${details != null ? ' - $details' : ''}';
  }
}

class NetworkError extends ApiError {
  const NetworkError({
    String message = 'Network connection failed',
    String? details,
  }) : super(message: message, details: details);
}

class RateLimitError extends ApiError {
  const RateLimitError({
    String message = 'API rate limit exceeded',
    int? statusCode,
    String? details,
  }) : super(message: message, statusCode: statusCode, details: details);
}

class InvalidSymbolError extends ApiError {
  const InvalidSymbolError({
    String message = 'Invalid stock symbol',
    String? details,
  }) : super(message: message, details: details);
}

class ApiKeyError extends ApiError {
  const ApiKeyError({
    String message = 'Invalid or missing API key',
    int? statusCode,
    String? details,
  }) : super(message: message, statusCode: statusCode, details: details);
}
