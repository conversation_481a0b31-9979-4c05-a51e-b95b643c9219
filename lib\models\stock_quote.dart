import 'package:json_annotation/json_annotation.dart';

part 'stock_quote.g.dart';

@JsonSerializable()
class StockQuote {
  @Json<PERSON><PERSON>(name: 'c')
  final double currentPrice;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'd')
  final double change;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'dp')
  final double percentChange;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'h')
  final double highPrice;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'l')
  final double lowPrice;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'o')
  final double openPrice;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'pc')
  final double previousClose;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 't')
  final int timestamp;

  const StockQuote({
    required this.currentPrice,
    required this.change,
    required this.percentChange,
    required this.highPrice,
    required this.lowPrice,
    required this.openPrice,
    required this.previousClose,
    required this.timestamp,
  });

  factory StockQuote.fromJson(Map<String, dynamic> json) =>
      _$StockQuoteFromJson(json);

  Map<String, dynamic> toJson() => _$StockQuoteToJson(this);

  bool get isPositive => change >= 0;
  
  String get formattedPrice => '\$${currentPrice.toStringAsFixed(2)}';
  
  String get formattedChange => '${isPositive ? '+' : ''}\$${change.abs().toStringAsFixed(2)}';
  
  String get formattedPercentChange => '${isPositive ? '+' : ''}${percentChange.toStringAsFixed(2)}%';
}
