import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../config/api_config.dart';

class ApiKeySetupWidget extends StatefulWidget {
  final VoidCallback onApiKeyConfigured;
  
  const ApiKeySetupWidget({
    super.key,
    required this.onApiKeyConfigured,
  });

  @override
  State<ApiKeySetupWidget> createState() => _ApiKeySetupWidgetState();
}

class _ApiKeySetupWidgetState extends State<ApiKeySetupWidget> {
  final TextEditingController _apiKeyController = TextEditingController();
  String? _errorMessage;
  bool _isValidating = false;

  @override
  void dispose() {
    _apiKeyController.dispose();
    super.dispose();
  }

  void _validateAndSaveApiKey() async {
    final apiKey = _apiKeyController.text.trim();
    
    setState(() {
      _isValidating = true;
      _errorMessage = null;
    });

    final validationError = ApiKeyValidator.validateApiKey(apiKey);
    
    if (validationError != null) {
      setState(() {
        _errorMessage = validationError;
        _isValidating = false;
      });
      return;
    }

    // Here you would typically save the API key securely
    // For this demo, we'll just proceed
    setState(() {
      _isValidating = false;
    });
    
    widget.onApiKeyConfigured();
  }

  void _openFinnhubWebsite() async {
    // In a real app, you would use url_launcher package
    // For now, we'll just show a dialog with instructions
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Get Your API Key'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('To get your free Finnhub API key:'),
            SizedBox(height: 8),
            Text('1. Visit https://finnhub.io/register'),
            Text('2. Create a free account'),
            Text('3. Copy your API key from the dashboard'),
            Text('4. Paste it in the field below'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Clipboard.setData(const ClipboardData(text: 'https://finnhub.io/register'));
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('URL copied to clipboard'),
                ),
              );
            },
            child: const Text('Copy URL'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // App Icon/Logo
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.blue,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Icon(
                  Icons.trending_up,
                  size: 40,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 24),
              
              // Title
              const Text(
                'Investment Analyzer',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 8),
              
              // Subtitle
              const Text(
                'Real-time stock market analysis',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 48),
              
              // Setup Card
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    const Text(
                      'Setup Required',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Enter your Finnhub API key to get started with real-time stock data.',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                    const SizedBox(height: 24),
                    
                    // API Key Input
                    TextField(
                      controller: _apiKeyController,
                      decoration: InputDecoration(
                        labelText: 'Finnhub API Key',
                        hintText: 'Enter your API key here',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        prefixIcon: const Icon(Icons.key),
                        errorText: _errorMessage,
                      ),
                      obscureText: true,
                    ),
                    const SizedBox(height: 16),
                    
                    // Get API Key Button
                    OutlinedButton.icon(
                      onPressed: _openFinnhubWebsite,
                      icon: const Icon(Icons.open_in_new),
                      label: const Text('Get Free API Key'),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    // Continue Button
                    ElevatedButton(
                      onPressed: _isValidating ? null : _validateAndSaveApiKey,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: _isValidating
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : const Text(
                              'Continue',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Info Text
              const Text(
                'Your API key is stored securely on your device and is only used to fetch stock data from Finnhub.',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
