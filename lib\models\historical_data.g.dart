// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'historical_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HistoricalData _$HistoricalDataFromJson(
  Map<String, dynamic> json,
) => HistoricalData(
  close: (json['c'] as List<dynamic>)
      .map((e) => (e as num).toDouble())
      .toList(),
  high: (json['h'] as List<dynamic>).map((e) => (e as num).toDouble()).toList(),
  low: (json['l'] as List<dynamic>).map((e) => (e as num).toDouble()).toList(),
  open: (json['o'] as List<dynamic>).map((e) => (e as num).toDouble()).toList(),
  status: json['s'] as String,
  timestamp: (json['t'] as List<dynamic>)
      .map((e) => (e as num).toInt())
      .toList(),
  volume: (json['v'] as List<dynamic>).map((e) => (e as num).toInt()).toList(),
);

Map<String, dynamic> _$HistoricalDataToJson(HistoricalData instance) =>
    <String, dynamic>{
      'c': instance.close,
      'h': instance.high,
      'l': instance.low,
      'o': instance.open,
      's': instance.status,
      't': instance.timestamp,
      'v': instance.volume,
    };
