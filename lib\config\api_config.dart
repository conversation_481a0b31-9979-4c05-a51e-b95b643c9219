class ApiConfig {
  // TODO: Replace with your actual Finnhub API key
  // Get your free API key from: https://finnhub.io/register
  static const String finnhubApiKey = 'co30iv9r01qp2simgtngco30iv9r01qp2simgto0';
  
  // Validate API key
  static bool get isApiKeyValid => 
      finnhubApiKey.isNotEmpty && 
      finnhubApiKey != 'YOUR_FINNHUB_API_KEY_HERE' &&
      finnhubApiKey.length > 10;
  
  // API endpoints
  static const String baseUrl = 'https://finnhub.io/api/v1';
  
  // Rate limiting configuration
  static const Duration requestInterval = Duration(milliseconds: 100);
  static const int maxRetries = 3;
  static const Duration requestTimeout = Duration(seconds: 10);
}

class ApiKeyValidator {
  static String? validateApiKey(String? apiKey) {
    if (apiKey == null || apiKey.isEmpty) {
      return 'API key is required';
    }
    
    if (apiKey == 'YOUR_FINNHUB_API_KEY_HERE') {
      return 'Please replace with your actual Finnhub API key';
    }
    
    if (apiKey.length < 10) {
      return 'API key appears to be invalid (too short)';
    }
    
    return null; // Valid
  }
  
  static bool isValidApiKey(String? apiKey) {
    return validateApiKey(apiKey) == null;
  }
}
