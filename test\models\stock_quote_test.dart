import 'package:flutter_test/flutter_test.dart';
import 'package:ultimateinvestmentanalyzer/models/stock_quote.dart';

void main() {
  group('StockQuote Tests', () {
    test('creates StockQuote from JSON', () {
      final json = {
        'c': 150.0,
        'd': 2.5,
        'dp': 1.69,
        'h': 152.0,
        'l': 148.0,
        'o': 149.0,
        'pc': 147.5,
        't': 1234567890,
      };

      final quote = StockQuote.fromJson(json);

      expect(quote.currentPrice, 150.0);
      expect(quote.change, 2.5);
      expect(quote.percentChange, 1.69);
      expect(quote.highPrice, 152.0);
      expect(quote.lowPrice, 148.0);
      expect(quote.openPrice, 149.0);
      expect(quote.previousClose, 147.5);
      expect(quote.timestamp, 1234567890);
    });

    test('converts StockQuote to JSON', () {
      const quote = StockQuote(
        currentPrice: 150.0,
        change: 2.5,
        percentChange: 1.69,
        highPrice: 152.0,
        lowPrice: 148.0,
        openPrice: 149.0,
        previousClose: 147.5,
        timestamp: 1234567890,
      );

      final json = quote.toJson();

      expect(json['c'], 150.0);
      expect(json['d'], 2.5);
      expect(json['dp'], 1.69);
      expect(json['h'], 152.0);
      expect(json['l'], 148.0);
      expect(json['o'], 149.0);
      expect(json['pc'], 147.5);
      expect(json['t'], 1234567890);
    });

    test('isPositive returns true for positive change', () {
      const quote = StockQuote(
        currentPrice: 150.0,
        change: 2.5,
        percentChange: 1.69,
        highPrice: 152.0,
        lowPrice: 148.0,
        openPrice: 149.0,
        previousClose: 147.5,
        timestamp: 1234567890,
      );

      expect(quote.isPositive, true);
    });

    test('isPositive returns false for negative change', () {
      const quote = StockQuote(
        currentPrice: 145.0,
        change: -2.5,
        percentChange: -1.69,
        highPrice: 152.0,
        lowPrice: 148.0,
        openPrice: 149.0,
        previousClose: 147.5,
        timestamp: 1234567890,
      );

      expect(quote.isPositive, false);
    });

    test('isPositive returns true for zero change', () {
      const quote = StockQuote(
        currentPrice: 147.5,
        change: 0.0,
        percentChange: 0.0,
        highPrice: 152.0,
        lowPrice: 148.0,
        openPrice: 149.0,
        previousClose: 147.5,
        timestamp: 1234567890,
      );

      expect(quote.isPositive, true);
    });

    test('formatted price includes dollar sign and two decimals', () {
      const quote = StockQuote(
        currentPrice: 150.123,
        change: 2.5,
        percentChange: 1.69,
        highPrice: 152.0,
        lowPrice: 148.0,
        openPrice: 149.0,
        previousClose: 147.5,
        timestamp: 1234567890,
      );

      expect(quote.formattedPrice, '\$150.12');
    });

    test('formatted change includes sign and dollar sign', () {
      const positiveQuote = StockQuote(
        currentPrice: 150.0,
        change: 2.5,
        percentChange: 1.69,
        highPrice: 152.0,
        lowPrice: 148.0,
        openPrice: 149.0,
        previousClose: 147.5,
        timestamp: 1234567890,
      );

      const negativeQuote = StockQuote(
        currentPrice: 145.0,
        change: -2.5,
        percentChange: -1.69,
        highPrice: 152.0,
        lowPrice: 148.0,
        openPrice: 149.0,
        previousClose: 147.5,
        timestamp: 1234567890,
      );

      expect(positiveQuote.formattedChange, '+\$2.50');
      expect(negativeQuote.formattedChange, '\$2.50');
    });

    test('formatted percent change includes sign and percent symbol', () {
      const positiveQuote = StockQuote(
        currentPrice: 150.0,
        change: 2.5,
        percentChange: 1.69,
        highPrice: 152.0,
        lowPrice: 148.0,
        openPrice: 149.0,
        previousClose: 147.5,
        timestamp: 1234567890,
      );

      const negativeQuote = StockQuote(
        currentPrice: 145.0,
        change: -2.5,
        percentChange: -1.69,
        highPrice: 152.0,
        lowPrice: 148.0,
        openPrice: 149.0,
        previousClose: 147.5,
        timestamp: 1234567890,
      );

      expect(positiveQuote.formattedPercentChange, '+1.69%');
      expect(negativeQuote.formattedPercentChange, '-1.69%');
    });
  });
}
