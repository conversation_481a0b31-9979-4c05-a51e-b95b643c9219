// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'stock_quote.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StockQuote _$StockQuoteFromJson(Map<String, dynamic> json) => StockQuote(
  currentPrice: (json['c'] as num).toDouble(),
  change: (json['d'] as num).toDouble(),
  percentChange: (json['dp'] as num).toDouble(),
  highPrice: (json['h'] as num).toDouble(),
  lowPrice: (json['l'] as num).toDouble(),
  openPrice: (json['o'] as num).toDouble(),
  previousClose: (json['pc'] as num).toDouble(),
  timestamp: (json['t'] as num).toInt(),
);

Map<String, dynamic> _$StockQuoteToJson(StockQuote instance) =>
    <String, dynamic>{
      'c': instance.currentPrice,
      'd': instance.change,
      'dp': instance.percentChange,
      'h': instance.highPrice,
      'l': instance.lowPrice,
      'o': instance.openPrice,
      'pc': instance.previousClose,
      't': instance.timestamp,
    };
