import 'package:flutter_test/flutter_test.dart';
import 'package:http/http.dart' as http;
import 'package:ultimateinvestmentanalyzer/services/finnhub_api_service.dart';
import 'package:ultimateinvestmentanalyzer/models/api_error.dart';

// Mock HTTP client for testing
class MockHttpClient extends http.BaseClient {
  final Map<String, http.Response> responses;
  
  MockHttpClient(this.responses);
  
  @override
  Future<http.StreamedResponse> send(http.BaseRequest request) async {
    final url = request.url.toString();
    
    if (responses.containsKey(url)) {
      final response = responses[url]!;
      return http.StreamedResponse(
        Stream.value(response.bodyBytes),
        response.statusCode,
        headers: response.headers,
      );
    }
    
    return http.StreamedResponse(
      Stream.value([]),
      404,
    );
  }
}

void main() {
  group('FinnhubApiService Tests', () {
    late FinnhubApiService apiService;
    late MockHttpClient mockClient;

    setUp(() {
      mockClient = MockHttpClient({});
      apiService = FinnhubApiService(
        apiKey: 'test_api_key',
        client: mockClient,
      );
    });

    tearDown(() {
      apiService.dispose();
    });

    group('getStockQuote', () {
      test('returns StockQuote on successful response', () async {
        // Mock successful response
        final mockResponse = http.Response(
          '{"c": 150.0, "d": 2.5, "dp": 1.69, "h": 152.0, "l": 148.0, "o": 149.0, "pc": 147.5, "t": 1234567890}',
          200,
        );
        
        mockClient.responses['https://finnhub.io/api/v1/quote?token=test_api_key&symbol=AAPL'] = mockResponse;

        final result = await apiService.getStockQuote('AAPL');

        expect(result.currentPrice, 150.0);
        expect(result.change, 2.5);
        expect(result.percentChange, 1.69);
        expect(result.isPositive, true);
      });

      test('throws InvalidSymbolError for invalid symbol', () async {
        // Mock response with null current price
        final mockResponse = http.Response(
          '{"c": null, "d": null, "dp": null, "h": null, "l": null, "o": null, "pc": null, "t": null}',
          200,
        );
        
        mockClient.responses['https://finnhub.io/api/v1/quote?token=test_api_key&symbol=INVALID'] = mockResponse;

        expect(
          () => apiService.getStockQuote('INVALID'),
          throwsA(isA<InvalidSymbolError>()),
        );
      });

      test('throws ApiKeyError for 401 response', () async {
        final mockResponse = http.Response('Unauthorized', 401);
        mockClient.responses['https://finnhub.io/api/v1/quote?token=test_api_key&symbol=AAPL'] = mockResponse;

        expect(
          () => apiService.getStockQuote('AAPL'),
          throwsA(isA<ApiKeyError>()),
        );
      });

      test('throws RateLimitError for 429 response', () async {
        final mockResponse = http.Response('Rate limit exceeded', 429);
        mockClient.responses['https://finnhub.io/api/v1/quote?token=test_api_key&symbol=AAPL'] = mockResponse;

        expect(
          () => apiService.getStockQuote('AAPL'),
          throwsA(isA<RateLimitError>()),
        );
      });
    });

    group('searchSymbols', () {
      test('returns SymbolSearchResponse on successful response', () async {
        final mockResponse = http.Response(
          '{"count": 1, "result": [{"description": "Apple Inc", "displaySymbol": "AAPL", "symbol": "AAPL", "type": "Common Stock"}]}',
          200,
        );
        
        mockClient.responses['https://finnhub.io/api/v1/search?token=test_api_key&q=AAPL'] = mockResponse;

        final result = await apiService.searchSymbols('AAPL');

        expect(result.count, 1);
        expect(result.result.length, 1);
        expect(result.result.first.symbol, 'AAPL');
        expect(result.result.first.description, 'Apple Inc');
      });

      test('handles empty search results', () async {
        final mockResponse = http.Response(
          '{"count": 0, "result": []}',
          200,
        );
        
        mockClient.responses['https://finnhub.io/api/v1/search?token=test_api_key&q=NONEXISTENT'] = mockResponse;

        final result = await apiService.searchSymbols('NONEXISTENT');

        expect(result.count, 0);
        expect(result.result.isEmpty, true);
      });
    });

    group('getHistoricalData', () {
      test('returns HistoricalData on successful response', () async {
        final mockResponse = http.Response(
          '{"c": [150.0, 151.0], "h": [152.0, 153.0], "l": [148.0, 149.0], "o": [149.0, 150.0], "s": "ok", "t": [1234567890, 1234567900], "v": [1000, 1100]}',
          200,
        );
        
        final from = DateTime.fromMillisecondsSinceEpoch(1234567890 * 1000);
        final to = DateTime.fromMillisecondsSinceEpoch(1234567900 * 1000);
        
        mockClient.responses['https://finnhub.io/api/v1/stock/candle?token=test_api_key&symbol=AAPL&resolution=D&from=1234567890&to=1234567900'] = mockResponse;

        final result = await apiService.getHistoricalData('AAPL', 'D', from, to);

        expect(result.status, 'ok');
        expect(result.close.length, 2);
        expect(result.close.first, 150.0);
        expect(result.close.last, 151.0);
      });

      test('throws InvalidSymbolError for no_data response', () async {
        final mockResponse = http.Response(
          '{"s": "no_data"}',
          200,
        );
        
        final from = DateTime.fromMillisecondsSinceEpoch(1234567890 * 1000);
        final to = DateTime.fromMillisecondsSinceEpoch(1234567900 * 1000);
        
        mockClient.responses['https://finnhub.io/api/v1/stock/candle?token=test_api_key&symbol=INVALID&resolution=D&from=1234567890&to=1234567900'] = mockResponse;

        expect(
          () => apiService.getHistoricalData('INVALID', 'D', from, to),
          throwsA(isA<InvalidSymbolError>()),
        );
      });
    });
  });
}
