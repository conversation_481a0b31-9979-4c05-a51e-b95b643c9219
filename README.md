# Investment Analyzer

A real-time stock market analysis Flutter application powered by the Finnhub API.

## Features

- **Real-time Stock Data**: Get current stock prices, daily changes, and market information
- **Stock Search**: Search for stocks by ticker symbol or company name with autocomplete suggestions
- **Historical Performance**: View 1-day, 1-week, 1-month, and 1-year performance data
- **Clean UI**: Simple, intuitive interface with color-coded indicators for gains/losses
- **Recent Searches**: Quick access to previously searched stocks
- **Error Handling**: Comprehensive error handling for network issues and invalid symbols

## Screenshots

The app features a clean, minimalistic design similar to Yahoo Finance with:
- Search functionality with autocomplete
- Real-time price display with color-coded changes
- Historical performance grid
- Today's trading range information

## Setup Instructions

### 1. Get a Finnhub API Key

1. Visit [https://finnhub.io/register](https://finnhub.io/register)
2. Create a free account
3. Copy your API key from the dashboard

### 2. Configure the API Key

1. Open `lib/config/api_config.dart`
2. Replace `YOUR_FINNHUB_API_KEY_HERE` with your actual API key:

```dart
static const String finnhubApiKey = 'your_actual_api_key_here';
```

### 3. Install Dependencies

```bash
flutter pub get
```

### 4. Run the App

```bash
flutter run
```

## API Endpoints Used

- **Stock Quote**: `/quote` - Real-time stock prices and daily changes
- **Symbol Search**: `/search` - Search for stocks by name or symbol
- **Historical Data**: `/stock/candle` - Historical price data for performance calculations

## Dependencies

- `http`: HTTP client for API requests
- `provider`: State management
- `json_annotation`: JSON serialization
- `shared_preferences`: Local storage for recent searches
- `intl`: Number and currency formatting

## Project Structure

```
lib/
├── config/
│   └── api_config.dart          # API configuration
├── models/
│   ├── stock_quote.dart         # Stock quote data model
│   ├── symbol_search_result.dart # Search result model
│   ├── historical_data.dart     # Historical data model
│   └── api_error.dart          # Error handling models
├── providers/
│   └── stock_provider.dart     # State management
├── services/
│   ├── finnhub_api_service.dart # API service layer
│   └── performance_service.dart # Performance calculations
├── screens/
│   └── home_screen.dart        # Main app screen
├── widgets/
│   ├── stock_search_widget.dart # Search functionality
│   ├── stock_data_widget.dart   # Stock data display
│   └── api_key_setup_widget.dart # API key setup
└── main.dart                   # App entry point
```

## Error Handling

The app includes comprehensive error handling for:
- Network connectivity issues
- Invalid API keys
- Rate limiting
- Invalid stock symbols
- API server errors

## Rate Limiting

The app implements rate limiting to respect Finnhub's API limits:
- Minimum 100ms between requests
- Automatic retry logic
- Request timeout handling

## Free Tier Limitations

Finnhub's free tier includes:
- 60 API calls per minute
- Real-time data for US stocks
- Basic company information
- Historical data access

For production use, consider upgrading to a paid plan for higher rate limits and additional features.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
