import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../models/stock_quote.dart';
import '../models/symbol_search_result.dart';
import '../models/historical_data.dart';
import '../models/api_error.dart';

class FinnhubApiService {
  static const String _baseUrl = 'https://finnhub.io/api/v1';
  final String _apiKey;
  final http.Client _client;
  
  // Rate limiting
  DateTime? _lastRequestTime;
  static const Duration _minRequestInterval = Duration(milliseconds: 100);

  FinnhubApiService({
    required String apiKey,
    http.Client? client,
  }) : _apiKey = apiKey,
       _client = client ?? http.Client();

  Future<void> _enforceRateLimit() async {
    if (_lastRequestTime != null) {
      final timeSinceLastRequest = DateTime.now().difference(_lastRequestTime!);
      if (timeSinceLastRequest < _minRequestInterval) {
        final waitTime = _minRequestInterval - timeSinceLastRequest;
        await Future.delayed(waitTime);
      }
    }
    _lastRequestTime = DateTime.now();
  }

  Future<Map<String, dynamic>> _makeRequest(String endpoint, {Map<String, String>? queryParams}) async {
    await _enforceRateLimit();
    
    final uri = Uri.parse('$_baseUrl$endpoint').replace(
      queryParameters: {
        'token': _apiKey,
        ...?queryParams,
      },
    );

    try {
      final response = await _client.get(uri).timeout(
        const Duration(seconds: 10),
        onTimeout: () => throw const NetworkError(
          message: 'Request timeout',
          details: 'The request took too long to complete',
        ),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data as Map<String, dynamic>;
      } else if (response.statusCode == 401) {
        throw const ApiKeyError(
          message: 'Invalid API key',
          statusCode: 401,
          details: 'Please check your Finnhub API key',
        );
      } else if (response.statusCode == 429) {
        throw const RateLimitError(
          message: 'Rate limit exceeded',
          statusCode: 429,
          details: 'Too many requests. Please try again later.',
        );
      } else {
        throw ApiError(
          message: 'API request failed',
          statusCode: response.statusCode,
          details: response.body,
        );
      }
    } on SocketException {
      throw const NetworkError(
        message: 'No internet connection',
        details: 'Please check your network connection and try again',
      );
    } on FormatException {
      throw const ApiError(
        message: 'Invalid response format',
        details: 'The server returned an invalid response',
      );
    }
  }

  Future<StockQuote> getStockQuote(String symbol) async {
    try {
      final data = await _makeRequest('/quote', queryParams: {'symbol': symbol.toUpperCase()});
      
      // Check if the response contains valid data
      if (data['c'] == null || data['c'] == 0) {
        throw InvalidSymbolError(
          message: 'Invalid stock symbol: $symbol',
          details: 'The symbol "$symbol" was not found or has no data available',
        );
      }
      
      return StockQuote.fromJson(data);
    } catch (e) {
      if (e is ApiError) rethrow;
      throw ApiError(
        message: 'Failed to fetch stock quote',
        details: e.toString(),
      );
    }
  }

  Future<SymbolSearchResponse> searchSymbols(String query) async {
    try {
      final data = await _makeRequest('/search', queryParams: {'q': query});
      return SymbolSearchResponse.fromJson(data);
    } catch (e) {
      if (e is ApiError) rethrow;
      throw ApiError(
        message: 'Failed to search symbols',
        details: e.toString(),
      );
    }
  }

  Future<HistoricalData> getHistoricalData(
    String symbol,
    String resolution,
    DateTime from,
    DateTime to,
  ) async {
    try {
      final data = await _makeRequest('/stock/candle', queryParams: {
        'symbol': symbol.toUpperCase(),
        'resolution': resolution,
        'from': (from.millisecondsSinceEpoch ~/ 1000).toString(),
        'to': (to.millisecondsSinceEpoch ~/ 1000).toString(),
      });
      
      if (data['s'] == 'no_data') {
        throw InvalidSymbolError(
          message: 'No historical data available',
          details: 'No historical data found for symbol "$symbol" in the specified time range',
        );
      }
      
      return HistoricalData.fromJson(data);
    } catch (e) {
      if (e is ApiError) rethrow;
      throw ApiError(
        message: 'Failed to fetch historical data',
        details: e.toString(),
      );
    }
  }

  void dispose() {
    _client.close();
  }
}
