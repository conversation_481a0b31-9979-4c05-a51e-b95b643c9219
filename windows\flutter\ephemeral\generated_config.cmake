# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\git-projects\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\git-projects\\investmentanalysis\\ultimateinvestmentanalyzer" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\Users\\<USER>\\git-projects\\flutter"
  "PROJECT_DIR=C:\\Users\\<USER>\\git-projects\\investmentanalysis\\ultimateinvestmentanalyzer"
  "FLUTTER_ROOT=C:\\Users\\<USER>\\git-projects\\flutter"
  "FLUTTER_EPHEMERAL_DIR=C:\\Users\\<USER>\\git-projects\\investmentanalysis\\ultimateinvestmentanalyzer\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=C:\\Users\\<USER>\\git-projects\\investmentanalysis\\ultimateinvestmentanalyzer"
  "FLUTTER_TARGET=C:\\Users\\<USER>\\git-projects\\investmentanalysis\\ultimateinvestmentanalyzer\\lib\\main.dart"
  "DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzUuNg==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049OWY0NTVkMjQ4Ng==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049ZDI5MTM2MzJhNA==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My45LjI="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=C:\\Users\\<USER>\\git-projects\\investmentanalysis\\ultimateinvestmentanalyzer\\.dart_tool\\package_config.json"
)
