import 'package:json_annotation/json_annotation.dart';

part 'historical_data.g.dart';

@JsonSerializable()
class HistoricalData {
  @Json<PERSON>ey(name: 'c')
  final List<double> close;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'h')
  final List<double> high;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'l')
  final List<double> low;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'o')
  final List<double> open;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 's')
  final String status;
  
  @<PERSON>son<PERSON>ey(name: 't')
  final List<int> timestamp;
  
  @<PERSON>son<PERSON>ey(name: 'v')
  final List<int> volume;

  const HistoricalData({
    required this.close,
    required this.high,
    required this.low,
    required this.open,
    required this.status,
    required this.timestamp,
    required this.volume,
  });

  factory HistoricalData.fromJson(Map<String, dynamic> json) =>
      _$HistoricalDataFromJson(json);

  Map<String, dynamic> toJson() => _$HistoricalDataToJson(this);

  double? getPercentChange() {
    if (close.isEmpty || close.length < 2) return null;
    
    final currentPrice = close.last;
    final previousPrice = close.first;
    
    return ((currentPrice - previousPrice) / previousPrice) * 100;
  }
}

class PerformanceData {
  final double? oneDayChange;
  final double? oneWeekChange;
  final double? oneMonthChange;
  final double? oneYearChange;

  const PerformanceData({
    this.oneDayChange,
    this.oneWeekChange,
    this.oneMonthChange,
    this.oneYearChange,
  });

  String formatPercentChange(double? change) {
    if (change == null) return 'N/A';
    final isPositive = change >= 0;
    return '${isPositive ? '+' : ''}${change.toStringAsFixed(2)}%';
  }
}
