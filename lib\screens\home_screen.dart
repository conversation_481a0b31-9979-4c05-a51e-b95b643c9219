import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/stock_provider.dart';
import '../widgets/stock_search_widget.dart';
import '../widgets/stock_data_widget.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'Investment Analyzer',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.blue,
        elevation: 0,
        centerTitle: true,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              // Search Widget
              const StockSearchWidget(),
              const SizedBox(height: 24),
              
              // Stock Data Widget
              Expanded(
                child: SingleChildScrollView(
                  child: Consumer<StockProvider>(
                    builder: (context, provider, child) {
                      return const StockDataWidget();
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: Consumer<StockProvider>(
        builder: (context, provider, child) {
          if (provider.hasData) {
            return FloatingActionButton(
              onPressed: () {
                if (provider.currentSymbol != null) {
                  provider.fetchStockData(provider.currentSymbol!);
                }
              },
              backgroundColor: Colors.blue,
              child: const Icon(
                Icons.refresh,
                color: Colors.white,
              ),
            );
          }
          return const SizedBox.shrink();
        },
      ),
    );
  }
}
