import 'package:json_annotation/json_annotation.dart';

part 'symbol_search_result.g.dart';

@JsonSerializable()
class SymbolSearchResult {
  final String description;
  final String displaySymbol;
  final String symbol;
  final String type;

  const SymbolSearchResult({
    required this.description,
    required this.displaySymbol,
    required this.symbol,
    required this.type,
  });

  factory SymbolSearchResult.fromJson(Map<String, dynamic> json) =>
      _$SymbolSearchResultFromJson(json);

  Map<String, dynamic> toJson() => _$SymbolSearchResultToJson(this);
}

@JsonSerializable()
class SymbolSearchResponse {
  final int count;
  final List<SymbolSearchResult> result;

  const SymbolSearchResponse({
    required this.count,
    required this.result,
  });

  factory SymbolSearchResponse.fromJson(Map<String, dynamic> json) =>
      _$SymbolSearchResponseFromJson(json);

  Map<String, dynamic> toJson() => _$SymbolSearchResponseToJson(this);
}
