import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'config/api_config.dart';
import 'services/finnhub_api_service.dart';
import 'providers/stock_provider.dart';
import 'screens/home_screen.dart';
import 'widgets/api_key_setup_widget.dart';

void main() {
  runApp(const InvestmentAnalyzerApp());
}

class InvestmentAnalyzerApp extends StatelessWidget {
  const InvestmentAnalyzerApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Investment Analyzer',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          elevation: 0,
        ),
      ),
      home: const AppWrapper(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class AppWrapper extends StatefulWidget {
  const AppWrapper({super.key});

  @override
  State<AppWrapper> createState() => _AppWrapperState();
}

class _AppWrapperState extends State<AppWrapper> {
  bool _isApiKeyConfigured = false;

  @override
  void initState() {
    super.initState();
    _checkApiKeyConfiguration();
  }

  void _checkApiKeyConfiguration() {
    setState(() {
      _isApiKeyConfigured = ApiConfig.isApiKeyValid;
    });
  }

  void _onApiKeyConfigured() {
    setState(() {
      _isApiKeyConfigured = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (!_isApiKeyConfigured) {
      return ApiKeySetupWidget(
        onApiKeyConfigured: _onApiKeyConfigured,
      );
    }

    return ChangeNotifierProvider(
      create: (context) => StockProvider(
        FinnhubApiService(apiKey: ApiConfig.finnhubApiKey),
      ),
      child: const HomeScreen(),
    );
  }
}


