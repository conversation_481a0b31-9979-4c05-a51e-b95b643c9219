// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'symbol_search_result.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SymbolSearchResult _$SymbolSearchResultFromJson(Map<String, dynamic> json) =>
    SymbolSearchResult(
      description: json['description'] as String,
      displaySymbol: json['displaySymbol'] as String,
      symbol: json['symbol'] as String,
      type: json['type'] as String,
    );

Map<String, dynamic> _$SymbolSearchResultToJson(SymbolSearchResult instance) =>
    <String, dynamic>{
      'description': instance.description,
      'displaySymbol': instance.displaySymbol,
      'symbol': instance.symbol,
      'type': instance.type,
    };

SymbolSearchResponse _$SymbolSearchResponseFromJson(
  Map<String, dynamic> json,
) => SymbolSearchResponse(
  count: (json['count'] as num).toInt(),
  result: (json['result'] as List<dynamic>)
      .map((e) => SymbolSearchResult.fromJson(e as Map<String, dynamic>))
      .toList(),
);

Map<String, dynamic> _$SymbolSearchResponseToJson(
  SymbolSearchResponse instance,
) => <String, dynamic>{'count': instance.count, 'result': instance.result};
