import '../models/historical_data.dart';
import '../services/finnhub_api_service.dart';

class PerformanceService {
  final FinnhubApiService _apiService;

  PerformanceService(this._apiService);

  Future<PerformanceData> getPerformanceData(String symbol, double currentPrice) async {
    final now = DateTime.now();
    
    try {
      // Calculate different time periods
      final oneDayAgo = now.subtract(const Duration(days: 1));
      final oneWeekAgo = now.subtract(const Duration(days: 7));
      final oneMonthAgo = now.subtract(const Duration(days: 30));
      final oneYearAgo = now.subtract(const Duration(days: 365));

      // Fetch historical data for different periods
      final futures = await Future.wait([
        _getPercentChangeForPeriod(symbol, oneDayAgo, now, currentPrice, 'D'),
        _getPercentChangeForPeriod(symbol, oneWeekAgo, now, currentPrice, 'D'),
        _getPercentChangeForPeriod(symbol, oneMonthAgo, now, currentPrice, 'D'),
        _getPercentChangeForPeriod(symbol, oneYearAgo, now, currentPrice, 'W'),
      ], eagerError: false);

      return PerformanceData(
        oneDayChange: futures[0],
        oneWeekChange: futures[1],
        oneMonthChange: futures[2],
        oneYearChange: futures[3],
      );
    } catch (e) {
      // Return partial data if some requests fail
      return const PerformanceData();
    }
  }

  Future<double?> _getPercentChangeForPeriod(
    String symbol,
    DateTime from,
    DateTime to,
    double currentPrice,
    String resolution,
  ) async {
    try {
      final historicalData = await _apiService.getHistoricalData(
        symbol,
        resolution,
        from,
        to,
      );

      if (historicalData.close.isEmpty) return null;

      final pastPrice = historicalData.close.first;
      return ((currentPrice - pastPrice) / pastPrice) * 100;
    } catch (e) {
      return null;
    }
  }
}
