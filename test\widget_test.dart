import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:ultimateinvestmentanalyzer/providers/stock_provider.dart';
import 'package:ultimateinvestmentanalyzer/services/finnhub_api_service.dart';
import 'package:ultimateinvestmentanalyzer/widgets/stock_search_widget.dart';
import 'package:ultimateinvestmentanalyzer/widgets/stock_data_widget.dart';

void main() {
  group('Investment Analyzer App Tests', () {
    testWidgets('App loads with search widget', (WidgetTester tester) async {
      // Create a mock API service for testing
      final mockApiService = FinnhubApiService(apiKey: 'test_key');

      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider(
            create: (context) => StockProvider(mockApiService),
            child: const Scaffold(
              body: Column(
                children: [
                  StockSearchWidget(),
                  Expanded(child: StockDataWidget()),
                ],
              ),
            ),
          ),
        ),
      );

      // Verify that the search widget is present
      expect(find.byType(StockSearchWidget), findsOneWidget);
      expect(find.byType(StockDataWidget), findsOneWidget);

      // Verify search field is present
      expect(find.byType(TextField), findsOneWidget);
      expect(find.text('Search stocks (e.g., AAPL, Apple)'), findsOneWidget);
    });

    testWidgets('Search field accepts input', (WidgetTester tester) async {
      final mockApiService = FinnhubApiService(apiKey: 'test_key');

      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider(
            create: (context) => StockProvider(mockApiService),
            child: const Scaffold(
              body: StockSearchWidget(),
            ),
          ),
        ),
      );

      // Find the search field and enter text
      final searchField = find.byType(TextField);
      await tester.enterText(searchField, 'AAPL');
      await tester.pump();

      // Verify the text was entered
      expect(find.text('AAPL'), findsOneWidget);
    });

    testWidgets('Empty state shows search message', (WidgetTester tester) async {
      final mockApiService = FinnhubApiService(apiKey: 'test_key');

      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider(
            create: (context) => StockProvider(mockApiService),
            child: const Scaffold(
              body: StockDataWidget(),
            ),
          ),
        ),
      );

      // Verify empty state message is shown
      expect(find.text('Search for a stock to view its data'), findsOneWidget);
      expect(find.byIcon(Icons.search), findsOneWidget);
    });
  });
}
